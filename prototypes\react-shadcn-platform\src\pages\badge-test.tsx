import React from 'react';
import { Badge } from '@/components/ui/badge';
import { PlaceholderDash } from '@/components/ui/placeholder-dash';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Briefcase, Star, Clock, Eye } from 'lucide-react';

export default function BadgeTestPage() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Badge Component Test Page</h1>
          <p className="text-muted-foreground">
            Testing all badge variants to ensure they render correctly without placeholder dashes
          </p>
        </div>

        {/* Basic Badge Variants */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Badge Variants</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Badge variant="default">Default Badge</Badge>
              <Badge variant="secondary">Secondary Badge</Badge>
              <Badge variant="destructive">Destructive Badge</Badge>
              <Badge variant="outline">Outline Badge</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Grade Badges */}
        <Card>
          <CardHeader>
            <CardTitle>Grade Badge Variants</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Badge variant="grade-a">Grade A</Badge>
              <Badge variant="grade-b">Grade B</Badge>
              <Badge variant="grade-c">Grade C</Badge>
              <Badge variant="grade-d">Grade D</Badge>
              <Badge variant="grade-f">Grade F</Badge>
              <Badge variant="neutral">Neutral</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Domain/Technology Badges */}
        <Card>
          <CardHeader>
            <CardTitle>Domain & Technology Badges</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline">Technology</Badge>
              <Badge variant="outline">Software Engineering</Badge>
              <Badge variant="outline">Artificial Intelligence</Badge>
              <Badge variant="outline">Data Science</Badge>
              <Badge variant="outline">Product Management</Badge>
              <Badge variant="outline">Design</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Badges with Icons */}
        <Card>
          <CardHeader>
            <CardTitle>Badges with Icons</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Badge variant="default">
                <Briefcase className="h-4 w-4 mr-1" />
                Job Offer
              </Badge>
              <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                <Star className="h-3 w-3 mr-1" />
                Featured
              </Badge>
              <Badge variant="secondary">
                <Clock className="h-3 w-3 mr-1" />
                Expires in 5 days
              </Badge>
              <Badge variant="outline">
                <Eye className="h-3 w-3 mr-1" />
                156 views
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Count Badges */}
        <Card>
          <CardHeader>
            <CardTitle>Count Badges</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Badge count={5} variant="default" />
              <Badge count={12} variant="secondary" />
              <Badge count={0} showZero={true} variant="outline" />
              <Badge count={150} max={99} variant="destructive" />
            </div>
          </CardContent>
        </Card>

        {/* Content Prop Badges */}
        <Card>
          <CardHeader>
            <CardTitle>Content Prop Badges</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Badge content="Technology" variant="outline" />
              <Badge content="High Priority" variant="destructive" />
              <Badge content="Active" variant="default" />
              <Badge content="Expires Soon" variant="secondary" />
            </div>
          </CardContent>
        </Card>

        {/* Size Variants */}
        <Card>
          <CardHeader>
            <CardTitle>Size Variants</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap items-center gap-2">
              <Badge size="sm" variant="default">Small Badge</Badge>
              <Badge size="default" variant="default">Default Badge</Badge>
              <Badge size="lg" variant="default">Large Badge</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Placeholder Dash Comparison */}
        <Card>
          <CardHeader>
            <CardTitle>Placeholder Dash vs Badge Comparison</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Placeholder Dashes:</span>
                <PlaceholderDash variant="default" />
                <PlaceholderDash variant="thick" />
                <PlaceholderDash variant="thin" />
                <PlaceholderDash variant="dot" />
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Actual Badges:</span>
                <Badge variant="outline">Technology</Badge>
                <Badge variant="default">Active</Badge>
                <Badge variant="secondary">Medium</Badge>
                <Badge variant="destructive">High</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Real-world Example */}
        <Card>
          <CardHeader>
            <CardTitle>Real-world Example (Browse Postings Style)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="border rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="default">
                  <Briefcase className="h-4 w-4" />
                  <span className="ml-1 capitalize">offer</span>
                </Badge>
                <Badge variant="destructive">
                  high priority
                </Badge>
                <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                  <Star className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              </div>
              <h3 className="text-lg font-semibold mb-2">Senior Software Engineer Position</h3>
              <div className="flex flex-wrap gap-2 mb-3">
                <Badge variant="outline" className="text-xs">React</Badge>
                <Badge variant="outline" className="text-xs">Node.js</Badge>
                <Badge variant="outline" className="text-xs">AWS</Badge>
                <Badge variant="outline" className="text-xs">Full-time</Badge>
              </div>
              <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  249d ago
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  156 views
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
